{"name": "@repo/ui", "version": "0.0.0", "private": true, "exports": {"./*": "./src/*.tsx", "./hooks/*": "./src/hooks/*.ts", "./globals.scss": "./src/css/globals.scss", "./radix-override.scss": "./src/css/radix-override.scss"}, "scripts": {"lint": "eslint . --max-warnings 0 --fix", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@turbo/gen": "^2.5.0", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.26.0", "typescript": "5.8.2", "tailwindcss": "^4"}, "dependencies": {"@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "react": "^19.1.0", "react-dom": "^19.1.0"}}