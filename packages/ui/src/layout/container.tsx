import { PropsWithChildren } from "react";
import classNames from "classnames";

export const Container = ({
  children,
  background,
  className,
  dataEffect,
  style,
  ref,
}: PropsWithChildren<{
  background?: React.ReactNode;
  className?: string;
  dataEffect?: string;
  style?: React.CSSProperties;
  ref?: React.Ref<HTMLDivElement>;
}>) => {
  return (
    <div
      ref={ref}
      data-effect={dataEffect}
      className={classNames("relative z-0", className)}
      style={style}
    >
      {background}
      <div
        className={classNames(
          "max-w-[1270px] mx-auto",
          "grid",
          "grid-cols-4 px-4 gap-y-0",
          "md:grid-cols-12 md:gap-6 md:px-6 md:gap-y-0"
        )}
      >
        {children}
      </div>
    </div>
  );
};
