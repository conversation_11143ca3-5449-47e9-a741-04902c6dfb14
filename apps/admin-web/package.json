{"name": "admin-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@repo/ui": "*", "@tanstack/react-query": "^5.67.2", "clsx": "^2.1.1", "next": "^15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "postcss": "^8", "typescript": "^5"}}