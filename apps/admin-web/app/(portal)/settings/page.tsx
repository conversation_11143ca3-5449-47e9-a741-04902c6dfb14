"use client";

import { <PERSON><PERSON>, But<PERSON>, Heading } from "@radix-ui/themes";
import { Container } from "@repo/ui/layout/container";
import { ProfileListItem } from "@repo/ui/profile-list-item";

export default function ProfileOverviewPage() {
  const {
    profilePictureUrl = "",
    firstName = "<PERSON> Zhen",
    lastName = "Yeoh",
    role = "admin",
    email = "<EMAIL>",
  } = {};

  const roleDisplay = { admin: "Admin" }[role];

  return (
    <main className="grow py-6 md:py-14">
      <Container>
        <div className="col-span-full md:col-span-8 md:col-start-3 flex flex-col">
          <div className="mb-6">
            <Avatar
              src={profilePictureUrl}
              radius="full"
              fallback={`${firstName.charAt(0)}${lastName.charAt(0)}`}
              color="gray"
              highContrast
              size="6"
            />
          </div>

          <div className="mb-8">
            <Heading as="h3" size="7" weight="bold">
              {firstName} {lastName}
            </Heading>
          </div>

          <ProfileListItem label="Email" value={email} />

          <ProfileListItem label="Role" value={roleDisplay} />

          <ProfileListItem
            label="Password"
            value={
              <Button size="1" radius="full" color="gray" variant="soft">
                Change password
              </Button>
            }
          />
        </div>
      </Container>
    </main>
  );
}
