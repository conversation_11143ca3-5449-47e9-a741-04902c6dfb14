"use client";
import { CaretDownIcon } from "@radix-ui/react-icons";
import { Avatar, Button } from "@radix-ui/themes";
import { AppMenu } from "@repo/ui/app-menu";
import Link from "next/link";

interface LinkConfig {
  href: string;
  label: string;
}

export const UserAvatar = () => {
  const { isAuthed = true } = {};

  const {
    profilePictureUrl = "",
    firstName = "Qi Zhen",
    lastName = "Yeoh",
  } = {};
  const fallback = firstName.charAt(0) + lastName.charAt(0);

  if (!isAuthed) return null;

  const config = [
    {
      href: "/settings",
      label: "Settings",
    },
    {
      href: "/logout",
      label: "Logout",
    },
  ];

  const renderLink = ({ href, label }: LinkConfig) => ({
    item: <Link href={href}>{label}</Link>,
    label,
  });

  return (
    <div className="flex gap-2 items-center">
      <Avatar
        src={profilePictureUrl}
        size="1"
        radius="full"
        fallback={fallback}
        color="gray"
      />

      <AppMenu linkConfig={config.map(renderLink)}>
        <Button
          radius="full"
          variant="soft"
          color="gray"
          style={{ background: "none" }}
        >
          <div className="flex gap-2 items-center">
            <span>
              {firstName} {lastName}
            </span>
            <CaretDownIcon />
          </div>
        </Button>
      </AppMenu>
    </div>
  );
};
