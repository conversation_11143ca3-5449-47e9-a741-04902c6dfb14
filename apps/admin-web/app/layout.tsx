import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON>, <PERSON>o_Mono } from "next/font/google";
import { Theme } from "@radix-ui/themes";
import { QueryProvider } from "./query-provider";
import "@radix-ui/themes/styles.css";
import "@repo/ui/radix-override.scss";
import "@repo/ui/globals.scss";
import { AdminNav } from "./components/admin-nav";
import { UserAvatar } from "./components/admin-avatar";

const robotoSans = Roboto({
  weight: ["100", "300", "400", "500", "700", "900"],
  variable: "--font-roboto-sans",
  subsets: ["latin"],
});

const robotoMono = Roboto_Mono({
  variable: "--font-roboto-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Oecusse Digital Centre - Admin Portal",
  description: "The Future of Blockchain-Powered Investment",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${robotoSans.variable} ${robotoMono.variable} antialiased`}
      >
        <QueryProvider>
          <Theme
            accentColor="orange"
            scaling="100%"
            grayColor="gray"
            className="w-full h-full flex flex-col grow"
          >
            <AdminNav>
              <UserAvatar />
            </AdminNav>
            {children}
          </Theme>
        </QueryProvider>
      </body>
    </html>
  );
}
