import { ArrowLeftIcon, ArrowRightIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";
import classNames from "classnames";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export const Pagination = ({
  currentPage,
  totalPages,
  onPageChange,
  className,
}: PaginationProps) => {
  const renderPageNumbers = () => {
    const pages: (number | string)[] = [];
    const showEllipsis = totalPages > 7;

    if (showEllipsis) {
      if (currentPage <= 3) {
        // Show first 3 pages, ellipsis, and last 2 pages
        pages.push(1, 2, 3, "...", totalPages - 1, totalPages);
      } else if (currentPage >= totalPages - 2) {
        // Show first 2 pages, ellipsis, and last 3 pages
        pages.push(1, 2, "...", totalPages - 2, totalPages - 1, totalPages);
      } else {
        // Show first page, ellipsis, current and adjacent pages, ellipsis, last page
        pages.push(
          1,
          "...",
          currentPage - 1,
          currentPage,
          currentPage + 1,
          "...",
          totalPages,
        );
      }
    } else {
      // Show all pages if total pages <= 7
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    }

    return pages.map((page, index) => {
      if (page === "...") {
        return (
          <span key={`ellipsis-${index}`} className="px-3 py-2 text-gray-400">
            ...
          </span>
        );
      }

      return (
        <Button
          key={page}
          radius="large"
          variant="soft"
          color="gray"
          style={currentPage === page ? {} : { background: "none" }}
          onClick={() => typeof page === "number" && onPageChange(page)}
          className={classNames(
            "min-w-[40px]",
            currentPage === page && "pointer-events-none",
          )}
        >
          {page}
        </Button>
      );
    });
  };

  return (
    <div
      className={classNames(
        "flex items-center justify-center gap-2",
        className,
      )}
    >
      <Button
        radius="large"
        variant="soft"
        color="gray"
        disabled={currentPage === 1}
        onClick={() => onPageChange(currentPage - 1)}
        style={{ background: "none" }}
      >
        <ArrowLeftIcon />
        Previous
      </Button>

      <div className="flex items-center gap-1">{renderPageNumbers()}</div>

      <Button
        radius="large"
        variant="soft"
        color="gray"
        disabled={currentPage === totalPages}
        onClick={() => onPageChange(currentPage + 1)}
        style={{ background: "none" }}
      >
        Next
        <ArrowRightIcon />
      </Button>
    </div>
  );
};
