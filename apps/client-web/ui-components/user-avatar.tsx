"use client";
import { CaretDownIcon } from "@radix-ui/react-icons";
import { Avatar, Button } from "@radix-ui/themes";
import { useIsSmallScreen } from "../hooks/use-screen";
import { useAuth } from "@/hooks/use-auth";
import { AppMenu } from "@repo/ui/app-menu";
import { LinkConfig, renderLink } from "./nav-link/utils";
import { useTranslations } from "next-intl";

export const UserAvatar = ({ linkConfig }: { linkConfig: LinkConfig[] }) => {
  const { isAuthed, userData } = useAuth({ requireAuth: true });
  const t = useTranslations("Nav");

  const isSmall = useIsSmallScreen();
  const { profilePictureUrl, firstName = "", lastName = "" } = userData;
  const fallback = firstName.charAt(0) + lastName.charAt(0);

  if (!isAuthed) return null;

  const config = [
    ...(isSmall ? linkConfig : []),

    {
      href: "/settings",
      label: t("settings"),
    },
    {
      href: "/logout",
      label: t("logout"),
    },
  ];

  return (
    <div className="flex gap-2 items-center">
      <Avatar
        src={profilePictureUrl}
        size="1"
        radius="full"
        fallback={fallback}
        color="gray"
      />

      <AppMenu linkConfig={config.map(renderLink)}>
        <Button
          radius="full"
          variant="soft"
          color="gray"
          style={{ background: "none" }}
        >
          <div className="flex gap-2 items-center">
            <span>
              {firstName} {lastName}
            </span>
            <CaretDownIcon />
          </div>
        </Button>
      </AppMenu>
    </div>
  );
};
