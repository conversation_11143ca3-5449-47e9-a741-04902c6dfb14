import Link from "next/link";
import LandingPageLayout from "./[locale]/(landingpage)/layout";
import { NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import { Container } from "@/ui-components/layout/container";
import Image from "next/image";
import { Button, Text } from "@radix-ui/themes";

export default async function NotFound() {
  const messages = await getMessages({ locale: "en" });

  return (
    <NextIntlClientProvider locale={"en"} messages={messages}>
      <LandingPageLayout>
        <Container className="pt-20 pb-30 bg-[#f0f0f0]">
          <div className="section-content flex flex-col items-center justify-center gap-6 text-center">
            <Image
              src="/graphics/orange/404.png"
              alt="404 Not Found"
              width={120}
              height={120}
            />
            <Text color="gray" size="2">
              {`The page you are trying to access doesn't exist or has been moved.`}
            </Text>
            <Button
              size="3"
              variant="soft"
              radius="full"
              color="gray"
              highContrast
              asChild
            >
              <Link href="/">Return to homepage</Link>
            </Button>
          </div>
        </Container>
      </LandingPageLayout>
    </NextIntlClientProvider>
  );
}
