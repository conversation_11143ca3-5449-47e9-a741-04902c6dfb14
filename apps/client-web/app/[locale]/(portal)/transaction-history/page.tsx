"use client";
import { Container } from "@/ui-components/layout/container";
import { Heading, SegmentedControl } from "@radix-ui/themes";
import { TransactionTable } from "./_components/transaction-table";
import { mockTransactions } from "./data";
import { Pagination } from "@/ui-components/pagination";
import { useState } from "react";
import { useTranslations } from "next-intl";

export default function TransactionHistoryPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = 12;
  const t = useTranslations("Portal.TransactionHistory");
  const tCommon = useTranslations("Common");

  const filterOptions = [
    {
      label: t("filters.all"),
      value: "all",
    },
    {
      label: t("filters.pending"),
      value: "pending",
    },
    {
      label: t("filters.verified"),
      value: "verified",
    },
    {
      label: t("filters.denied"),
      value: "denied",
    },
  ];

  return (
    <main className="grow py-6 md:py-10 bg-[#F2F2F2]">
      <Container>
        <div className="section-content flex flex-col gap-6">
          <Heading as="h2" size="7">
            {t("title")}
          </Heading>

          <div className="flex flex-col gap-4">
            <div>
              <SegmentedControl.Root defaultValue="all" variant="surface">
                {filterOptions.map((option) => (
                  <SegmentedControl.Item
                    key={option.value}
                    value={option.value}
                  >
                    {option.label}
                  </SegmentedControl.Item>
                ))}
              </SegmentedControl.Root>
            </div>

            <div className="flex flex-col gap-4">
              <TransactionTable data={mockTransactions} />
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                className="justify-end"
                nextText={tCommon("next")}
                previousText={tCommon("previous")}
              />
            </div>
          </div>
        </div>
      </Container>
    </main>
  );
}
