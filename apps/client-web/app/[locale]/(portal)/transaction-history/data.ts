export interface TransactionDTO {
  transactionId: string;
  createdDate: string;
  transactionHash: string;
  details: string;
  network: string;
  totalAmount: string;
  fee: string;
  status: "pending" | "verified" | "denied";
  blockedReason: string;
}

export const mockTransactions: TransactionDTO[] = [
  {
    transactionId: "c55e6d4e-a163-4fa4-8fd8-c1247487c155",
    createdDate: "2024/02/17 09:34:52",
    transactionHash:
      "0x8ad52c6dd31907f943a5c1d7d71 997d72961534d493146347d417d98",
    details: "Blockchain transfer to vault 1872",
    network: "USDT-TRON",
    totalAmount: "+19,000",
    fee: "0.02",
    status: "pending",
    blockedReason: "N/A",
  },
  {
    transactionId: "c55e6d4e-a163-4fa4-8fd8-c1247487c156",
    createdDate: "2024/02/17 09:34:52",
    transactionHash:
      "0x8ad52c6dd31907f943a5c1d7d71 997d72961534d493146347d417d99",
    details: "Blockchain transfer to vault 1872",
    network: "USDT-TRON",
    totalAmount: "+19,000",
    fee: "0.02",
    status: "verified",
    blockedReason: "N/A",
  },
  {
    transactionId: "c55e6d4e-a163-4fa4-8fd8-c1247487c157",
    createdDate: "2024/02/17 09:34:52",
    transactionHash:
      "0x8ad52c6dd31907f943a5c1d7d71 997d72961534d493146347d417d100",
    details: "Blockchain transfer to vault 1872",
    network: "USDT-TRON",
    totalAmount: "+19,000",
    fee: "0.02",
    status: "denied",
    blockedReason: "insufficient amount",
  },
  {
    transactionId: "c55e6d4e-a163-4fa4-8fd8-c1247487c158",
    createdDate: "2024/02/17 09:34:52",
    transactionHash: "",
    details: "bank transfer to same account UHSG3SXFGK7447",
    network: "FAST",
    totalAmount: "+19,000",
    fee: "0.02",
    status: "pending",
    blockedReason: "N/A",
  },
  {
    transactionId: "c55e6d4e-a163-4fa4-8fd8-c1247487c159",
    createdDate: "2024/02/17 09:34:52",
    transactionHash: "",
    details: "bank transfer to same account UHSG3SXFGK7447",
    network: "FAST",
    totalAmount: "+19,000",
    fee: "0.02",
    status: "verified",
    blockedReason: "N/A",
  },
  {
    transactionId: "c55e6d4e-a163-4fa4-8fd8-c1247487c160",
    createdDate: "2024/02/17 09:34:52",
    transactionHash: "",
    details: "bank transfer to same account UHSG3SXFGK7447",
    network: "FAST",
    totalAmount: "+19,000",
    fee: "0.02",
    status: "denied",
    blockedReason: "insufficient amount",
  },
  {
    transactionId: "c55e6d4e-a163-4fa4-8fd8-c1247487c161",
    createdDate: "2024/02/17 09:34:52",
    transactionHash: "",
    details: "bank transfer to same account UHSG3SXFGK7447",
    network: "FAST",
    totalAmount: "+19,000",
    fee: "0.02",
    status: "pending",
    blockedReason: "N/A",
  },
  {
    transactionId: "c55e6d4e-a163-4fa4-8fd8-c1247487c162",
    createdDate: "2024/02/17 09:34:52",
    transactionHash: "",
    details: "bank transfer to same account UHSG3SXFGK7447",
    network: "FAST",
    totalAmount: "+19,000",
    fee: "0.02",
    status: "verified",
    blockedReason: "N/A",
  },
  {
    transactionId: "c55e6d4e-a163-4fa4-8fd8-c1247487c163",
    createdDate: "2024/02/17 09:34:52",
    transactionHash: "",
    details: "bank transfer to same account UHSG3SXFGK7447",
    network: "FAST",
    totalAmount: "+19,000",
    fee: "0.02",
    status: "denied",
    blockedReason: "insufficient amount",
  },
  {
    transactionId: "c55e6d4e-a163-4fa4-8fd8-c1247487c164",
    createdDate: "2024/02/17 09:34:52",
    transactionHash: "",
    details: "bank transfer to same account UHSG3SXFGK7447",
    network: "FAST",
    totalAmount: "+19,000",
    fee: "0.02",
    status: "denied",
    blockedReason: "insufficient amount",
  },
];
