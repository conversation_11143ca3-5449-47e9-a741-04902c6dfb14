"use client";
import { PrimaryButton, SecondaryButton } from "@repo/ui/form-button";
import { InfoLayout } from "@/ui-components/info-layout";
import { StatusItem, StatusItemProps } from "./status-item";

export const AccountStatus = ({
  onStartVerification,
}: {
  onStartVerification: () => void;
}) => {
  const status: StatusItemProps[] = [
    {
      icon: "/graphics/orange/verify.png",
      title: "Step 1: Create an account",
      status: "complete",
    },
    {
      icon: "/graphics/orange/verify.png",
      title: "Step 2: Verifying your identity",
      status: "pending",
    },
    {
      icon: "/graphics/orange/verify.png",
      title: "Step 3: Link bank account",
      status: null,
    },
  ];

  return (
    <InfoLayout
      className="py-6 md:py-10"
      icon="/graphics/orange/verify.png"
      iconAlt="verify"
      title="Complete account set up"
      description={`Before you can deposit funds, regulations require us to gather more information about you. This helps keep your account secure.`}
    >
      <div>
        {status.map((item, index) => (
          <StatusItem key={index} {...item} />
        ))}
      </div>

      <div className="flex flex-col gap-4">
        <PrimaryButton onClick={onStartVerification}>
          Verify your identity
        </PrimaryButton>
        <SecondaryButton>Sign out</SecondaryButton>
      </div>
    </InfoLayout>
  );
};
