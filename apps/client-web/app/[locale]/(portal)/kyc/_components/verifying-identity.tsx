import { Link } from "@/i18n/navigation";
import { PrimaryButton, SecondaryButton } from "@repo/ui/form-button";
import { InfoLayout } from "@/ui-components/info-layout";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";

export const VerifyingIdentity = () => {
  const [showNext, setShowNext] = useState(false);
  const t = useTranslations("Portal.KYC.verifyingIdentity");
  const tCommon = useTranslations("Portal.KYC");

  useEffect(() => {
    // TODO: display according to real status
    setTimeout(() => {
      setShowNext(true);
    }, 3000);
  }, []);

  if (showNext) {
    return (
      <InfoLayout
        className="py-20 md:py-10 xl:py-20"
        icon="/graphics/orange/verified.png"
        iconAlt="verified"
        title={t("verifiedTitle")}
        description={t("verifiedDescription")}
      >
        <div className="flex justify-center">
          <PrimaryButton asChild>
            <Link href={"/dashboard"}>{t("continueToDashboard")}</Link>
          </PrimaryButton>
        </div>
      </InfoLayout>
    );
  }

  return (
    <InfoLayout
      className="py-20 md:py-10 xl:py-20"
      icon="/graphics/orange/pending.png"
      iconAlt="pending"
      title={t("verifyingTitle")}
      description={t("verifyingDescription")}
    >
      <div className="flex justify-center">
        <SecondaryButton>{tCommon("signOut")}</SecondaryButton>
      </div>
    </InfoLayout>
  );
};
