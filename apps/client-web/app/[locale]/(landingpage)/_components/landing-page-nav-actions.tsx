"use client";
import { useTranslations } from "next-intl";
import { HamburgerMenuIcon } from "@radix-ui/react-icons";
import { Button, DropdownMenu, IconButton } from "@radix-ui/themes";
import { API_BASE_URL } from "@/app/constants";
import { AppMenu, NavConfig } from "@repo/ui/app-menu";
import { PrimaryButton } from "@/ui-components/buttons";
import { useIsSmallScreen } from "@/hooks/use-screen";
import { Link } from "@/i18n/navigation";
import { useAuth } from "@/hooks/use-auth";

const goToLogin = () => {
  window.location.href = `${API_BASE_URL}/api/v1/auth/pre-login`;
};

const AuthButtons = ({ isAuthed }: { isAuthed: boolean }) => {
  const t = useTranslations();

  if (isAuthed) {
    return (
      <Button size="2" variant="soft" color="blue" asChild radius="full">
        <Link href="/dashboard">{t("Nav.dashboard")}</Link>
      </Button>
    );
  }

  return (
    <div className="flex gap-3">
      <Button
        size="2"
        variant="soft"
        color="blue"
        onClick={goToLogin}
        radius="full"
      >
        {t("Nav.login")}
      </Button>
      <PrimaryButton size="2" variant="solid" onClick={goToLogin}>
        {t("Nav.signup")}
      </PrimaryButton>
    </div>
  );
};

const AuthMenu = () => {
  const t = useTranslations();
  return (
    <>
      <DropdownMenu.Item key="login" onClick={goToLogin}>
        {t("Nav.login")}
      </DropdownMenu.Item>
      <DropdownMenu.Item key="signup" onClick={goToLogin}>
        {t("Nav.signup")}
      </DropdownMenu.Item>
    </>
  );
};

export const LandingPageMenu = ({
  linkConfig,
}: {
  linkConfig: NavConfig[];
}) => {
  const isSmall = useIsSmallScreen();
  const { isAuthed } = useAuth();

  if (!isSmall) {
    return <AuthButtons isAuthed={isAuthed} />;
  }
  return (
    <AppMenu linkConfig={linkConfig} action={<AuthMenu />}>
      <IconButton variant="soft" color="gray">
        <HamburgerMenuIcon />
      </IconButton>
    </AppMenu>
  );
};
