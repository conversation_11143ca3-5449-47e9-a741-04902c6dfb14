import Cookies from "js-cookie";
import { API_BASE_URL } from "@/app/constants";
import { Api } from "./Api";

const service = new Api({
  baseUrl: API_BASE_URL,
  customFetch: (url, options) => {
    const method = options?.method ?? "";
    const customizeOption: RequestInit = { credentials: "include" };

    if (!["GET", "HEAD", "TRACE", "OPTIONS"].includes(method)) {
      customizeOption.headers = {
        ...options?.headers,
        "X-XSRF-TOKEN": Cookies.get("XSRF-TOKEN") ?? "",
      };
    }

    return fetch(url, { ...options, ...customizeOption });
  },
});

export default service;
