/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface ResponseResultMapStringListString {
  /**
   * Business Code
   * @format int32
   * @default "0"
   */
  code?: number;
  message?: string;
  data?: Record<string, string[]>;
  requestId?: string;
}

export interface ResponseResultVoid {
  /**
   * Business Code
   * @format int32
   * @default "0"
   */
  code?: number;
  message?: string;
  data?: object;
  requestId?: string;
}

export interface KycOnboardingMultipartFormData {
  /** KYC metadata as JSON. This part must be application/json. */
  data?: KycVerificationRequestData;
  /**
   * Front of document (e.g. ID card/passport). This part is an octet-stream.
   * @format binary
   */
  documentFront?: File;
  /**
   * Back of document (optional). This part is an octet-stream.
   * @format binary
   */
  documentBack?: File;
  /**
   * Selfie photo. This part is an octet-stream.
   * @format binary
   */
  selfie?: File;
}

export interface KycVerificationRequestData {
  firstName: string;
  lastName: string;
  nationality: string;
  countryOfResidence: string;
  identityDocumentType: "NATIONAL_ID" | "DRIVERS_LICENSE" | "PASSPORT";
}

export interface SubmitFiatDepositMultipartFormData {
  /** FIAT deposit metadata as JSON. This part must be application/json. */
  data?: SubmitFiatDepositRequestData;
  /**
   * Proof of transfer document. This part is an octet-stream.
   * @format binary
   */
  proofOfTransfer?: File;
}

export interface SubmitFiatDepositRequestData {
  amount: number;
  /**
   * @minLength 1
   * @maxLength 255
   */
  bankName: string;
  /** @pattern ^\d+$ */
  accountNumber: string;
}

export interface SubmitCryptoDepositRequest {
  amount: number;
  userWalletAddress: string;
  transactionHash: string;
}

export interface ResponseResultUserProfileDTO {
  /**
   * Business Code
   * @format int32
   * @default "0"
   */
  code?: number;
  message?: string;
  data?: UserProfileDTO;
  requestId?: string;
}

export interface UserProfileDTO {
  publicId?: string;
  email?: string;
  /**
   * User status, not directly linked to KYC level
   * @default "0"
   */
  userStatus?:
    | "PENDING_KYC"
    | "ACTIVE"
    | "SUSPENDED"
    | "PERMANENTLY_RESTRICTED";
  /** @default "-" */
  firstName?: string;
  lastName?: string;
  profilePictureUrl?: string;
}

export interface IdentityDocumentTypeDTO {
  name?: "NATIONAL_ID" | "DRIVERS_LICENSE" | "PASSPORT";
}

export interface ResponseResultListIdentityDocumentTypeDTO {
  /**
   * Business Code
   * @format int32
   * @default "0"
   */
  code?: number;
  message?: string;
  data?: IdentityDocumentTypeDTO[];
  requestId?: string;
}

export interface CountryDTO {
  alpha2?: string;
  name?: string;
}

export interface ResponseResultListCountryDTO {
  /**
   * Business Code
   * @format int32
   * @default "0"
   */
  code?: number;
  message?: string;
  data?: CountryDTO[];
  requestId?: string;
}
