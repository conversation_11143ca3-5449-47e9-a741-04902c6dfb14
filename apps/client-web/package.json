{"name": "client-web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"api:gen": "npx swagger-typescript-api generate --path https://portal-portfolio-staging.up.railway.app/v3/api-docs --output ./api --modular", "dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0 --fix", "check-types": "tsc --noEmit"}, "dependencies": {"@repo/ui": "*", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@tanstack/react-query": "^5.67.2", "@types/country-list": "^2.1.4", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "classnames": "^2.5.1", "country-list": "^2.3.0", "install": "^0.13.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "next": "^15.3.2", "next-intl": "^3.26.5", "react": "^19.1.0", "react-dom": "^19.1.0", "sass": "^1.85.1"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.27.0", "typescript": "^5"}}